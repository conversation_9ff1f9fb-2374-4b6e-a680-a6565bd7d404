<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-users-cog mr-2"></i>
                        Interviewer Status Overview
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item active">Interviewer Status</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <?php if (empty($detailedData)): ?>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info-circle"></i> No Positions Available for Interview</h5>
                    <p>There are no positions marked for interview with shortlisted applicants. Please ensure:</p>
                    <ul>
                        <li>Positions are marked as "for interview" in the positions management</li>
                        <li>Positions have shortlisted applicants</li>
                        <li>Positions are active</li>
                    </ul>
                </div>
            <?php else: ?>
                <!-- Detailed Interviewer Status Table -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-table mr-2"></i>
                            Detailed Interviewer Status by Applicant
                        </h3>
                        <div class="card-tools">
                            <span class="badge badge-info">
                                <?= count($detailedData) ?> Records
                            </span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="interviewerStatusTable">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Position Group</th>
                                        <th>Position</th>
                                        <th>Interviewee</th>
                                        <th>Interviewer Status</th>
                                        <th>Last Updated</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($detailedData as $record): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($record['position_group']) ?></strong>
                                            </td>
                                            <td>
                                                <strong><?= esc($record['position_designation']) ?></strong><br>
                                                <small class="text-muted">
                                                    Code: <?= esc($record['position_no']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <strong><?= esc($record['applicant_name']) ?></strong><br>
                                                <small class="text-muted"><?= esc($record['applicant_contact']) ?></small>
                                            </td>
                                            <td>
                                                <?php if (!empty($record['interviewer_status'])): ?>
                                                    <?php foreach ($record['interviewer_status'] as $interviewerInfo): ?>
                                                        <div class="mb-1">
                                                            <small><?= esc($interviewerInfo['interviewer']['interviewer_name']) ?></small>
                                                            <?php if ($interviewerInfo['status'] == 'full'): ?>
                                                                <i class="fas fa-check-circle text-primary ml-1"
                                                                   title="Full data entered (<?= $interviewerInfo['entered_count'] ?>/<?= $interviewerInfo['total_questions'] ?> questions)"></i>
                                                                <small class="text-success">(Complete)</small>
                                                            <?php elseif ($interviewerInfo['status'] == 'partial'): ?>
                                                                <i class="fas fa-check-circle text-warning ml-1"
                                                                   title="Partial data entered (<?= $interviewerInfo['entered_count'] ?>/<?= $interviewerInfo['total_questions'] ?> questions)"></i>
                                                                <small class="text-warning">(<?= $interviewerInfo['total_questions'] - $interviewerInfo['entered_count'] ?> pending)</small>
                                                            <?php else: ?>
                                                                <i class="fas fa-check-circle text-danger ml-1"
                                                                   title="No data entered (0/<?= $interviewerInfo['total_questions'] ?> questions)"></i>
                                                                <small class="text-danger">(<?= $interviewerInfo['total_questions'] ?> pending)</small>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <small class="text-muted">No interviewers assigned</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($record['last_updated'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('interviews/data/view/' . $record['applicant_id']) ?>"
                                                   class="btn btn-success btn-sm" title="View Interview Data">
                                                    <i class="fas fa-clipboard-list"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Summary Card -->
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Summary Statistics
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-list"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Records</span>
                                        <span class="info-box-number"><?= count($detailedData) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Unique Positions</span>
                                        <span class="info-box-number">
                                            <?php
                                            $uniquePositions = [];
                                            foreach ($detailedData as $record) {
                                                $uniquePositions[$record['position_id']] = true;
                                            }
                                            echo count($uniquePositions);
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-user-graduate"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Applicants</span>
                                        <span class="info-box-number">
                                            <?php
                                            $uniqueApplicants = [];
                                            foreach ($detailedData as $record) {
                                                $uniqueApplicants[$record['applicant_id']] = true;
                                            }
                                            echo count($uniqueApplicants);
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-primary">
                                    <span class="info-box-icon"><i class="fas fa-layer-group"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Position Groups</span>
                                        <span class="info-box-number">
                                            <?php
                                            $uniqueGroups = [];
                                            foreach ($detailedData as $record) {
                                                $uniqueGroups[$record['position_group']] = true;
                                            }
                                            echo count($uniqueGroups);
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- DataTables CSS and JS for Excel export -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.3/css/buttons.bootstrap4.min.css">

<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.2.3/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.bootstrap4.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.html5.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable with Excel export functionality
    $('#interviewerStatusTable').DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false, // No pagination as requested
        "order": [[ 0, "asc" ]], // Sort by position group
        "columnDefs": [
            { "orderable": false, "targets": [5] } // Disable sorting for action column
        ],
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export to Excel',
                className: 'btn btn-success btn-sm',
                title: 'Interviewer Status Overview - ' + new Date().toLocaleDateString(),
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Export all columns except Action column
                }
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> Print',
                className: 'btn btn-info btn-sm',
                title: 'Interviewer Status Overview',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Print all columns except Action column
                }
            }
        ],
        "language": {
            "search": "Search records:",
            "lengthMenu": "Show _MENU_ records per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ records",
            "infoEmpty": "No records available",
            "infoFiltered": "(filtered from _MAX_ total records)"
        }
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Style the buttons
    $('.dt-buttons').addClass('mb-3');
    $('.dt-button').addClass('mr-2');
});
</script>
<?= $this->endSection() ?>
