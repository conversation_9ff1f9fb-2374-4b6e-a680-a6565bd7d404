<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-users-cog mr-2"></i>
                        Interviewer Status Overview
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item active">Interviewer Status</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <?php if (empty($interviewData)): ?>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info-circle"></i> No Positions Available for Interview</h5>
                    <p>There are no positions marked for interview with shortlisted applicants. Please ensure:</p>
                    <ul>
                        <li>Positions are marked as "for interview" in the positions management</li>
                        <li>Positions have shortlisted applicants</li>
                        <li>Positions are active</li>
                    </ul>
                </div>
            <?php else: ?>
                <?php foreach ($interviewData as $group): ?>
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-layer-group mr-2"></i>
                                <?= esc($group['group_name']) ?>
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-primary">
                                    Priority: <?= $group['priority'] ?>
                                </span>
                                <span class="badge badge-info ml-2">
                                    <?= count($group['positions']) ?> <?= count($group['positions']) > 1 ? 'Positions' : 'Position' ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th width="8%">Position No</th>
                                            <th width="25%">Position Details</th>
                                            <th width="10%" class="text-center">Shortlisted</th>
                                            <th width="8%" class="text-center">Questions</th>
                                            <th width="8%" class="text-center">Interviewers</th>
                                            <th width="25%" class="text-center">Interviewer Status</th>
                                            <th width="10%" class="text-center">Last Updated</th>
                                            <th width="6%" class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($group['positions'] as $position): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= esc($position['position_no']) ?></strong>
                                                </td>
                                                <td>
                                                    <strong><?= esc($position['designation']) ?></strong><br>
                                                    <small class="text-muted">
                                                        <?= esc($position['classification']) ?> | 
                                                        Award: <?= esc($position['award']) ?>
                                                    </small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-success badge-lg">
                                                        <?= $position['shortlisted_count'] ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-info">
                                                        <?= $position['questions_count'] ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-warning">
                                                        <?= $position['interviewers_count'] ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <?php if (!empty($position['interviewer_status'])): ?>
                                                        <?php foreach ($position['interviewer_status'] as $interviewerInfo): ?>
                                                            <div class="mb-1">
                                                                <small><?= esc($interviewerInfo['interviewer']['interviewer_name']) ?></small>
                                                                <?php if ($interviewerInfo['status'] == 'full'): ?>
                                                                    <i class="fas fa-check-circle text-primary ml-1" 
                                                                       title="Full data entered (<?= $interviewerInfo['percentage'] ?>% - <?= $interviewerInfo['actual_entries'] ?>/<?= $interviewerInfo['total_expected'] ?>)"></i>
                                                                <?php elseif ($interviewerInfo['status'] == 'partial'): ?>
                                                                    <i class="fas fa-check-circle text-warning ml-1" 
                                                                       title="Partial data entered (<?= $interviewerInfo['percentage'] ?>% - <?= $interviewerInfo['actual_entries'] ?>/<?= $interviewerInfo['total_expected'] ?>)"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-check-circle text-danger ml-1" 
                                                                       title="No data entered (<?= $interviewerInfo['percentage'] ?>% - <?= $interviewerInfo['actual_entries'] ?>/<?= $interviewerInfo['total_expected'] ?>)"></i>
                                                                <?php endif; ?>
                                                                <small class="text-muted">(<?= $interviewerInfo['percentage'] ?>%)</small>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <small class="text-muted">No setup</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <small class="text-muted">
                                                        <?= date('M d, Y', strtotime($position['updated_at'])) ?>
                                                    </small>
                                                </td>
                                                <td class="text-center">
                                                    <a href="<?= base_url('interviews/open/' . $position['id']) ?>"
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-door-open"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Summary Card -->
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>
                            Interviewer Status Summary
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-layer-group"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Position Groups</span>
                                        <span class="info-box-number"><?= count($interviewData) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-briefcase"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Positions</span>
                                        <span class="info-box-number">
                                            <?php 
                                            $totalPositions = 0;
                                            foreach ($interviewData as $group) {
                                                $totalPositions += count($group['positions']);
                                            }
                                            echo $totalPositions;
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Interviewers</span>
                                        <span class="info-box-number">
                                            <?php 
                                            $totalInterviewers = 0;
                                            foreach ($interviewData as $group) {
                                                foreach ($group['positions'] as $position) {
                                                    $totalInterviewers += $position['interviewers_count'];
                                                }
                                            }
                                            echo $totalInterviewers;
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-primary">
                                    <span class="info-box-icon"><i class="fas fa-user-graduate"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Applicants</span>
                                        <span class="info-box-number">
                                            <?php 
                                            $totalApplicants = 0;
                                            foreach ($interviewData as $group) {
                                                foreach ($group['positions'] as $position) {
                                                    $totalApplicants += $position['shortlisted_count'];
                                                }
                                            }
                                            echo $totalApplicants;
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTables for better table functionality
    $('.table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false,
        "order": [[ 0, "asc" ]], // Sort by position number
        "columnDefs": [
            { "orderable": false, "targets": [7] } // Disable sorting for action column
        ]
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?= $this->endSection() ?>
